import os
import sys
import subprocess
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Optional
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SadTalkerVideoGenerator:
    """Open-source video generator using SadTalker"""
    
    def __init__(self):
        self.sadtalker_path = Path("SadTalker")
        self.models_downloaded = False
        
    def setup_sadtalker(self):
        """Download and setup SadTalker if not already done"""
        logger.info("Setting up SadTalker...")
        
        if not self.sadtalker_path.exists():
            logger.info("Cloning SadTalker repository...")
            try:
                subprocess.run([
                    "git", "clone", 
                    "https://github.com/OpenTalker/SadTalker.git"
                ], check=True, cwd=".")
                logger.info("✅ SadTalker cloned successfully")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to clone <PERSON>Talker: {e}")
                return False
        
        # Install requirements
        requirements_file = self.sadtalker_path / "requirements.txt"
        if requirements_file.exists():
            logger.info("Installing SadTalker requirements...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
                ], check=True)
                logger.info("✅ Requirements installed")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to install requirements: {e}")
                return False
        
        return self.download_models()
    
    def download_models(self):
        """Download required models for SadTalker"""
        logger.info("Downloading SadTalker models...")
        
        models_dir = self.sadtalker_path / "checkpoints"
        models_dir.mkdir(exist_ok=True)
        
        # Required model files
        models = {
            "auido2exp_00300-model.pth": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/auido2exp_00300-model.pth",
            "auido2pose_00140-model.pth": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/auido2pose_00140-model.pth",
            "epoch_20.pth": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/epoch_20.pth",
            "facevid2vid_00189-model.pth.tar": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/facevid2vid_00189-model.pth.tar",
            "shape_predictor_68_face_landmarks.dat": "https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/shape_predictor_68_face_landmarks.dat"
        }
        
        for model_name, url in models.items():
            model_path = models_dir / model_name
            
            if model_path.exists():
                logger.info(f"✅ Model {model_name} already exists")
                continue
                
            logger.info(f"📥 Downloading {model_name}...")
            try:
                response = requests.get(url, stream=True)
                response.raise_for_status()
                
                with open(model_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.info(f"✅ Downloaded {model_name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to download {model_name}: {e}")
                return False
        
        self.models_downloaded = True
        logger.info("✅ All models downloaded successfully")
        return True
    
    def get_default_image(self):
        """Get or download a default avatar image"""
        default_img = Path("default_avatar.jpg")
        
        if not default_img.exists():
            logger.info("📥 Downloading default avatar image...")
            
            # Use a free avatar image
            avatar_url = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face"
            
            try:
                response = requests.get(avatar_url)
                response.raise_for_status()
                
                with open(default_img, 'wb') as f:
                    f.write(response.content)
                
                logger.info("✅ Default avatar downloaded")
                
            except Exception as e:
                logger.error(f"❌ Failed to download avatar: {e}")
                # Create a simple colored image as fallback
                self.create_simple_avatar(default_img)
        
        return default_img
    
    def create_simple_avatar(self, output_path):
        """Create a simple colored avatar as fallback"""
        try:
            from PIL import Image, ImageDraw
            
            # Create a simple avatar
            img = Image.new('RGB', (512, 512), color='lightblue')
            draw = ImageDraw.Draw(img)
            
            # Draw a simple face
            draw.ellipse([100, 100, 400, 400], fill='peachpuff', outline='black', width=3)
            draw.ellipse([150, 180, 200, 230], fill='black')  # Left eye
            draw.ellipse([300, 180, 350, 230], fill='black')  # Right eye
            draw.ellipse([225, 280, 275, 320], fill='pink')   # Nose
            draw.arc([180, 320, 320, 380], 0, 180, fill='black', width=5)  # Smile
            
            img.save(output_path)
            logger.info("✅ Created simple avatar")
            
        except ImportError:
            logger.error("❌ PIL not available, cannot create avatar")
            raise
    
    def generate_video(self, audio_file: Path, output_path: Optional[Path] = None, 
                      image_path: Optional[Path] = None) -> Path:
        """Generate talking head video using SadTalker"""
        
        if not self.models_downloaded:
            if not self.setup_sadtalker():
                raise RuntimeError("Failed to setup SadTalker")
        
        if output_path is None:
            output_path = Path("sadtalker_output.mp4")
        
        if image_path is None:
            image_path = self.get_default_image()
        
        if not audio_file.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_file}")
        
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        logger.info(f"🎬 Generating video with SadTalker...")
        logger.info(f"📸 Image: {image_path}")
        logger.info(f"🎵 Audio: {audio_file}")
        logger.info(f"📹 Output: {output_path}")
        
        # Prepare SadTalker command
        sadtalker_script = self.sadtalker_path / "inference.py"
        
        if not sadtalker_script.exists():
            raise FileNotFoundError(f"SadTalker inference script not found: {sadtalker_script}")
        
        # Create results directory
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        cmd = [
            sys.executable, str(sadtalker_script),
            "--driven_audio", str(audio_file),
            "--source_image", str(image_path),
            "--result_dir", str(results_dir),
            "--still",  # Use still image mode
            "--preprocess", "crop",  # Crop and align face
            "--enhancer", "gfpgan"  # Use face enhancer
        ]
        
        try:
            logger.info("🚀 Running SadTalker...")
            result = subprocess.run(
                cmd, 
                cwd=str(self.sadtalker_path),
                capture_output=True, 
                text=True, 
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ SadTalker completed successfully")
                
                # Find the generated video
                generated_files = list(results_dir.glob("**/*.mp4"))
                
                if generated_files:
                    generated_video = generated_files[0]
                    
                    # Move to desired output location
                    shutil.move(str(generated_video), str(output_path))
                    logger.info(f"✅ Video saved as: {output_path}")
                    
                    return output_path
                else:
                    raise RuntimeError("No video file generated by SadTalker")
            else:
                logger.error(f"❌ SadTalker failed:")
                logger.error(f"STDOUT: {result.stdout}")
                logger.error(f"STDERR: {result.stderr}")
                raise RuntimeError(f"SadTalker failed with return code {result.returncode}")
                
        except subprocess.TimeoutExpired:
            logger.error("❌ SadTalker timed out")
            raise RuntimeError("SadTalker generation timed out")
        except Exception as e:
            logger.error(f"❌ Error running SadTalker: {e}")
            raise

def test_sadtalker():
    """Test SadTalker setup and generation"""
    logger.info("🧪 Testing SadTalker Video Generation")
    logger.info("=" * 50)
    
    generator = SadTalkerVideoGenerator()
    
    # Create a test audio file (you can replace this with your actual audio)
    test_audio = Path("test_audio.wav")
    
    if not test_audio.exists():
        logger.info("📝 Creating test audio file...")
        # You would typically use your ElevenLabs generated audio here
        logger.warning("⚠️ No test audio file found. Please provide an audio file.")
        return False
    
    try:
        # Generate video
        output_video = generator.generate_video(test_audio)
        
        if output_video.exists():
            logger.info(f"🎉 SUCCESS! Video generated: {output_video}")
            logger.info(f"📊 File size: {output_video.stat().st_size} bytes")
            return True
        else:
            logger.error("❌ Video file not created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_sadtalker()
