#!/usr/bin/env python3
"""
Simple Open Source Video Generator
Creates videos by combining audio with static images or simple animations
No complex AI models required - works immediately!
"""

import os
import sys
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
from contextlib import contextmanager

import requests
from dotenv import load_dotenv
import google.generativeai as genai
from elevenlabs import ElevenLabs, VoiceSettings
from moviepy.editor import VideoFileClip, ImageClip, CompositeVideoClip, ColorClip
from instagrapi import Client
from PIL import Image, ImageDraw, ImageFont

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_video_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Config:
    """Configuration management class"""
    def __init__(self):
        self.gemini_api_key = self._get_required_env("GEMINI_API_KEY")
        self.elevenlabs_api_key = self._get_required_env("ELEVENLABS_API_KEY")
        self.ig_username = self._get_required_env("IG_USERNAME")
        self.ig_password = self._get_required_env("IG_PASSWORD")
        
        # Optional configuration with defaults
        self.voice_model = os.getenv("VOICE_MODEL", "eleven_monolingual_v1")
        self.voice_name = os.getenv("VOICE_NAME", "21m00Tcm4TlvDq8ikWAM")  # Rachel
        self.video_fps = int(os.getenv("VIDEO_FPS", "24"))
        
        # Voice quality settings
        self.voice_stability = float(os.getenv("VOICE_STABILITY", "0.71"))
        self.voice_similarity_boost = float(os.getenv("VOICE_SIMILARITY_BOOST", "0.5"))
        self.voice_style = float(os.getenv("VOICE_STYLE", "0.0"))
        self.voice_use_speaker_boost = os.getenv("VOICE_USE_SPEAKER_BOOST", "true").lower() == "true"

    def _get_required_env(self, key: str) -> str:
        """Get required environment variable or raise error"""
        value = os.getenv(key)
        if not value:
            raise ValueError(f"Required environment variable {key} not found")
        return value

@contextmanager
def temporary_directory():
    """Context manager for temporary directory"""
    temp_dir = Path(tempfile.mkdtemp())
    try:
        yield temp_dir
    finally:
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            logger.warning(f"Failed to cleanup temp directory {temp_dir}: {e}")

class SimpleVideoGenerator:
    """Simple video generator using static images and audio"""
    
    def __init__(self, config: Config):
        self.config = config
        self.setup_apis()
        
    def setup_apis(self):
        """Initialize API clients"""
        logger.info("Setting up APIs...")
        
        # Configure Gemini
        genai.configure(api_key=self.config.gemini_api_key)
        self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Configure ElevenLabs
        self.elevenlabs_client = ElevenLabs(api_key=self.config.elevenlabs_api_key)
        
        logger.info("APIs configured successfully")

    def generate_script(self, custom_prompt: Optional[str] = None, 
                       topic: Optional[str] = None, tone: str = "motivational", 
                       duration: int = 30) -> str:
        """Generate script using Gemini AI"""
        
        if custom_prompt:
            prompt = custom_prompt
        else:
            if not topic:
                topic = "productivity and success"
            
            prompt = f"""Create a {tone} {duration}-second video script about {topic}.
            
Requirements:
- Exactly {duration} seconds when spoken (approximately {duration * 2.5} words)
- {tone} and engaging tone
- Clear, conversational language
- Include a strong hook in the first 3 seconds
- End with a call to action
- No special formatting, just the spoken text
- Make it suitable for social media (Instagram/TikTok)

Topic: {topic}
Duration: {duration} seconds
Tone: {tone}

Script:"""

        logger.info(f"Generating {tone} script with Gemini...")
        
        try:
            response = self.gemini_model.generate_content(prompt)
            script = response.text.strip()
            
            # Estimate duration (rough calculation: ~2.5 words per second)
            word_count = len(script.split())
            estimated_duration = word_count / 2.5
            
            logger.info(f"Script generated successfully ({len(script)} characters, ~{word_count} words, ~{estimated_duration:.1f}s estimated)")
            return script
            
        except Exception as e:
            logger.error(f"Script generation failed: {e}")
            raise

    def generate_voice(self, text: str, output_path: Path) -> Path:
        """Generate voice using ElevenLabs"""
        logger.info(f"Generating voice for {len(text)} characters of text...")
        
        try:
            voice_settings = VoiceSettings(
                stability=self.config.voice_stability,
                similarity_boost=self.config.voice_similarity_boost,
                style=self.config.voice_style,
                use_speaker_boost=self.config.voice_use_speaker_boost
            )
            
            audio = self.elevenlabs_client.text_to_speech.convert(
                voice_id=self.config.voice_name,
                text=text,
                model_id=self.config.voice_model,
                voice_settings=voice_settings
            )

            # Save audio to file
            with open(output_path, "wb") as f:
                for chunk in audio:
                    f.write(chunk)
            
            logger.info(f"Voice generated successfully: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Voice generation failed: {e}")
            raise

    def create_avatar_image(self, output_path: Path) -> Path:
        """Create a simple avatar image"""
        logger.info("Creating avatar image...")
        
        try:
            # Create a professional-looking avatar
            img = Image.new('RGB', (1080, 1080), color='#2C3E50')  # Dark blue background
            draw = ImageDraw.Draw(img)
            
            # Draw avatar circle
            center_x, center_y = 540, 540
            radius = 300
            
            # Avatar background circle
            draw.ellipse([center_x - radius, center_y - radius, 
                         center_x + radius, center_y + radius], 
                        fill='#3498DB', outline='#FFFFFF', width=10)
            
            # Simple face features
            # Eyes
            eye_y = center_y - 80
            draw.ellipse([center_x - 80, eye_y - 30, center_x - 20, eye_y + 30], fill='#FFFFFF')
            draw.ellipse([center_x + 20, eye_y - 30, center_x + 80, eye_y + 30], fill='#FFFFFF')
            draw.ellipse([center_x - 65, eye_y - 15, center_x - 35, eye_y + 15], fill='#2C3E50')
            draw.ellipse([center_x + 35, eye_y - 15, center_x + 65, eye_y + 15], fill='#2C3E50')
            
            # Nose
            nose_y = center_y + 20
            draw.ellipse([center_x - 15, nose_y - 20, center_x + 15, nose_y + 20], fill='#E8F4FD')
            
            # Mouth (smile)
            mouth_y = center_y + 80
            draw.arc([center_x - 60, mouth_y - 30, center_x + 60, mouth_y + 30], 
                    0, 180, fill='#FFFFFF', width=8)
            
            # Add text overlay
            try:
                # Try to use a nice font, fall back to default if not available
                font = ImageFont.truetype("arial.ttf", 60)
            except:
                font = ImageFont.load_default()
            
            # Add motivational text
            text = "AI ASSISTANT"
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (1080 - text_width) // 2
            text_y = 950
            
            draw.text((text_x, text_y), text, fill='#FFFFFF', font=font)
            
            img.save(output_path)
            logger.info(f"Avatar image created: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to create avatar image: {e}")
            raise

    def create_video_with_audio(self, audio_file: Path, output_path: Path) -> Path:
        """Create video by combining audio with animated avatar"""
        logger.info("Creating video with audio and avatar...")
        
        try:
            # Create avatar image
            avatar_path = Path("temp_avatar.png")
            self.create_avatar_image(avatar_path)
            
            # Get audio duration
            from moviepy.editor import AudioFileClip
            audio_clip = AudioFileClip(str(audio_file))
            duration = audio_clip.duration
            
            # Create video clip from image
            video_clip = ImageClip(str(avatar_path), duration=duration)
            
            # Add some simple animation (zoom effect)
            def zoom_effect(t):
                zoom = 1 + 0.1 * (t / duration)  # Gradual zoom
                return zoom
            
            video_clip = video_clip.resize(zoom_effect)
            
            # Set video dimensions for Instagram (9:16)
            video_clip = video_clip.resize((1080, 1920))
            
            # Add audio
            final_clip = video_clip.set_audio(audio_clip)
            
            # Write video file
            final_clip.write_videofile(
                str(output_path),
                fps=self.config.video_fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Clean up
            audio_clip.close()
            video_clip.close()
            final_clip.close()
            avatar_path.unlink()
            
            logger.info(f"Video created successfully: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Video creation failed: {e}")
            raise

    def post_to_instagram(self, video_path: Path, caption: str) -> bool:
        """Post video to Instagram"""
        logger.info("Posting to Instagram...")
        
        try:
            cl = Client()
            cl.login(self.config.ig_username, self.config.ig_password)
            
            # Upload video
            media = cl.clip_upload(str(video_path), caption)
            
            logger.info(f"Successfully posted to Instagram: {media.pk}")
            return True
            
        except Exception as e:
            logger.error(f"Instagram posting failed: {e}")
            return False

    def generate_complete_video(self, custom_prompt: Optional[str] = None,
                              topic: Optional[str] = None, tone: str = "motivational",
                              duration: int = 30, auto_post: bool = True) -> Dict[str, Any]:
        """Complete video generation pipeline"""
        results = {
            "success": False,
            "script": None,
            "voice_file": None,
            "video_file": None,
            "posted": False,
            "errors": []
        }

        try:
            with temporary_directory() as temp_dir:
                # Generate script
                script = self.generate_script(
                    custom_prompt=custom_prompt,
                    topic=topic,
                    tone=tone,
                    duration=duration
                )
                results["script"] = script

                # Generate voice
                voice_file = temp_dir / "voice.mp3"
                voice_file = self.generate_voice(script, voice_file)
                results["voice_file"] = str(voice_file)

                # Create video
                video_file = Path("simple_video_output.mp4")
                video_file = self.create_video_with_audio(voice_file, video_file)
                results["video_file"] = str(video_file)

                # Post to Instagram if requested
                if auto_post:
                    posted = self.post_to_instagram(video_file, script)
                    results["posted"] = posted

                results["success"] = True
                logger.info("🎉 Simple video generation completed successfully!")

        except Exception as e:
            error_msg = f"Pipeline failed: {e}"
            logger.error(error_msg)
            results["errors"].append(error_msg)

        return results

def main():
    """Main execution function"""
    logger.info("🚀 Starting simple video generation...")
    
    try:
        config = Config()
        generator = SimpleVideoGenerator(config)
        
        # Generate video
        results = generator.generate_complete_video(
            topic="productivity tips",
            tone="energetic", 
            duration=30,
            auto_post=False  # Set to True to auto-post
        )
        
        if results["success"]:
            print(f"\n🎉 Video generation successful!")
            print(f"📹 Video: {results['video_file']}")
            print(f"📝 Script: {results['script'][:100]}...")
        else:
            print(f"\n❌ Video generation failed!")
            for error in results["errors"]:
                print(f"Error: {error}")
        
        return 0 if results["success"] else 1
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n❌ Video generation failed!")
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
