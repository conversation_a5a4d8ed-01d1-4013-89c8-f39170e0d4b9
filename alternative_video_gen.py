import requests
import os
import json
import time
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

def try_direct_video_creation():
    """Try creating video without uploading audio file first"""
    print("🎬 Trying direct video creation (without file upload)...")
    
    did_key = os.getenv("DID_API_KEY")
    headers = {"Authorization": f"Basic {did_key}"}
    
    # Try using a public audio URL instead of uploading
    test_audio_url = "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav"
    
    payload = {
        "script": {
            "type": "audio",
            "audio_url": test_audio_url
        },
        "source_url": "https://create-images-results.d-id.com/DefaultFemale.png",
        "config": {
            "fluent": True,
            "pad_audio": 0.5,
            "stitch": True,
            "result_format": "mp4"
        }
    }
    
    try:
        response = requests.post(
            "https://api.d-id.com/talks",
            headers={**headers, "Content-Type": "application/json"},
            data=json.dumps(payload),
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            talk_id = data.get('id')
            print(f"✅ Video creation started! ID: {talk_id}")
            return talk_id
        else:
            print(f"❌ Failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def try_text_to_speech_video():
    """Try creating video with text-to-speech instead of audio file"""
    print("\n🗣️ Trying text-to-speech video creation...")
    
    did_key = os.getenv("DID_API_KEY")
    headers = {"Authorization": f"Basic {did_key}"}
    
    payload = {
        "script": {
            "type": "text",
            "input": "Hello! This is a test video created with D-ID API. If you can see this, the authentication is working correctly!",
            "provider": {
                "type": "microsoft",
                "voice_id": "en-US-JennyNeural"
            }
        },
        "source_url": "https://create-images-results.d-id.com/DefaultFemale.png",
        "config": {
            "fluent": True,
            "pad_audio": 0.5,
            "stitch": True,
            "result_format": "mp4"
        }
    }
    
    try:
        response = requests.post(
            "https://api.d-id.com/talks",
            headers={**headers, "Content-Type": "application/json"},
            data=json.dumps(payload),
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            talk_id = data.get('id')
            print(f"✅ Video creation started! ID: {talk_id}")
            return talk_id
        else:
            print(f"❌ Failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def monitor_video_creation(talk_id):
    """Monitor video creation progress"""
    if not talk_id:
        return None
        
    print(f"\n⏳ Monitoring video creation for ID: {talk_id}")
    
    did_key = os.getenv("DID_API_KEY")
    headers = {"Authorization": f"Basic {did_key}"}
    
    max_wait = 300  # 5 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(
                f"https://api.d-id.com/talks/{talk_id}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status')
                
                print(f"📊 Status: {status}")
                
                if status == 'done':
                    video_url = data.get('result_url')
                    print(f"🎉 Video completed! URL: {video_url}")
                    return video_url
                elif status == 'error':
                    print(f"❌ Video creation failed: {data.get('error', 'Unknown error')}")
                    return None
                elif status in ['created', 'started']:
                    print(f"⏳ Video is being processed...")
                    time.sleep(10)
                else:
                    print(f"❓ Unknown status: {status}")
                    time.sleep(5)
            else:
                print(f"❌ Status check failed: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error checking status: {e}")
            time.sleep(5)
    
    print(f"⏰ Timeout waiting for video completion")
    return None

def download_video(video_url, output_path="test_video.mp4"):
    """Download the generated video"""
    if not video_url:
        return False
        
    print(f"\n⬇️ Downloading video from: {video_url}")
    
    try:
        response = requests.get(video_url, timeout=60)
        
        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Video saved as: {output_path}")
            print(f"📊 File size: {Path(output_path).stat().st_size} bytes")
            return True
        else:
            print(f"❌ Download failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Download error: {e}")
        return False

def main():
    print("🚀 Alternative D-ID Video Generation Test")
    print("=" * 50)
    
    # Check credits first
    did_key = os.getenv("DID_API_KEY")
    headers = {"Authorization": f"Basic {did_key}"}
    
    try:
        credits_resp = requests.get("https://api.d-id.com/credits", headers=headers)
        if credits_resp.status_code == 200:
            credits_data = credits_resp.json()
            remaining = credits_data.get('remaining', 0)
            print(f"💰 Credits remaining: {remaining}")
            
            if remaining < 1:
                print("❌ Insufficient credits for video generation")
                return
        else:
            print("⚠️ Could not check credits, proceeding anyway...")
    except Exception as e:
        print(f"⚠️ Error checking credits: {e}")
    
    # Try different video creation methods
    talk_id = None
    
    # Method 1: Text-to-speech (most likely to work)
    talk_id = try_text_to_speech_video()
    
    # Method 2: Direct video creation with public audio URL
    if not talk_id:
        talk_id = try_direct_video_creation()
    
    # Monitor and download if successful
    if talk_id:
        video_url = monitor_video_creation(talk_id)
        
        if video_url:
            success = download_video(video_url)
            
            if success:
                print(f"\n🎉 SUCCESS! Video generation working!")
                print(f"💡 You can now use D-ID API for video generation")
                print(f"📝 Note: Use text-to-speech instead of file upload")
            else:
                print(f"\n⚠️ Video created but download failed")
        else:
            print(f"\n❌ Video creation failed or timed out")
    else:
        print(f"\n❌ Could not start video creation")
        print(f"💡 Try getting a proper API key from D-ID dashboard")

if __name__ == "__main__":
    main()
