#!/usr/bin/env python3
"""
D-ID API Debugging Script
Comprehensive testing to identify and fix D-ID API issues
"""

import os
import requests
import json
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment_setup():
    """Test if environment variables are properly loaded"""
    print("🔧 Testing Environment Setup")
    print("=" * 50)
    
    did_key = os.getenv("DID_API_KEY")
    
    if not did_key:
        print("❌ DID_API_KEY not found in environment")
        print("💡 Check your .env file exists and contains DID_API_KEY=your_key")
        return False
    
    print(f"✅ DID_API_KEY found: {did_key[:10]}...{did_key[-4:] if len(did_key) > 14 else did_key}")
    print(f"📊 Key length: {len(did_key)} characters")
    
    # Check key format
    if did_key.startswith('did_'):
        print("✅ Key format looks correct (starts with 'did_')")
    else:
        print("⚠️ Key format unusual (doesn't start with 'did_')")
    
    return True

def test_basic_api_connection():
    """Test basic connection to D-ID API"""
    print("\n🌐 Testing Basic API Connection")
    print("=" * 50)
    
    did_key = os.getenv("DID_API_KEY")
    if not did_key:
        print("❌ No API key available for testing")
        return False
    
    headers = {
        "Authorization": f"Basic {did_key}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test with a simple GET request to check authentication
        print("📡 Testing authentication with D-ID API...")
        response = requests.get(
            "https://api.d-id.com/talks",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Authentication successful!")
            data = response.json()
            print(f"📊 Response: {json.dumps(data, indent=2)[:200]}...")
            return True
        elif response.status_code == 401:
            print("❌ Authentication failed - Invalid API key")
            print("💡 Check if your D-ID API key is correct")
        elif response.status_code == 403:
            print("❌ Forbidden - API key valid but no permissions")
            print("💡 Check if your D-ID account has sufficient credits")
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(f"📄 Response: {response.text}")
        
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False

def test_file_upload():
    """Test file upload to D-ID"""
    print("\n📁 Testing File Upload")
    print("=" * 50)
    
    did_key = os.getenv("DID_API_KEY")
    if not did_key:
        print("❌ No API key available for testing")
        return False
    
    # Create a small test audio file
    test_audio_path = Path("test_audio.mp3")
    
    try:
        # Create a minimal MP3 file for testing (just headers)
        with open(test_audio_path, "wb") as f:
            # Write minimal MP3 header
            f.write(b'\xff\xfb\x90\x00')  # MP3 frame header
            f.write(b'\x00' * 100)  # Some data
        
        print(f"📄 Created test file: {test_audio_path} ({test_audio_path.stat().st_size} bytes)")
        
        headers = {
            "Authorization": f"Basic {did_key}"
            # Note: Don't set Content-Type for file uploads
        }
        
        print("📤 Attempting file upload...")
        
        with open(test_audio_path, "rb") as f:
            files = {"file": ("test.mp3", f, "audio/mpeg")}
            
            response = requests.post(
                "https://api.d-id.com/uploads",
                headers=headers,
                files=files,
                timeout=30
            )
        
        print(f"📊 Upload Status Code: {response.status_code}")
        print(f"📊 Upload Response: {response.text}")
        
        if response.status_code == 201:
            print("✅ File upload successful!")
            data = response.json()
            print(f"📄 Upload URL: {data.get('url', 'N/A')}")
            return True
        else:
            print(f"❌ Upload failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Upload test error: {e}")
        return False
    finally:
        # Clean up test file
        if test_audio_path.exists():
            test_audio_path.unlink()
            print("🧹 Cleaned up test file")

def test_account_info():
    """Test account information and credits"""
    print("\n👤 Testing Account Information")
    print("=" * 50)
    
    did_key = os.getenv("DID_API_KEY")
    if not did_key:
        print("❌ No API key available for testing")
        return False
    
    headers = {
        "Authorization": f"Basic {did_key}",
        "Content-Type": "application/json"
    }
    
    try:
        # Try to get account/usage information
        endpoints_to_test = [
            ("Account Info", "https://api.d-id.com/account"),
            ("Usage Info", "https://api.d-id.com/usage"),
            ("Credits Info", "https://api.d-id.com/credits")
        ]
        
        for name, url in endpoints_to_test:
            print(f"📡 Testing {name}...")
            try:
                response = requests.get(url, headers=headers, timeout=10)
                print(f"  📊 {name} Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"  ✅ {name}: {json.dumps(data, indent=2)[:150]}...")
                else:
                    print(f"  ❌ {name} failed: {response.text[:100]}...")
                    
            except Exception as e:
                print(f"  ❌ {name} error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Account test error: {e}")
        return False

def test_alternative_endpoints():
    """Test alternative D-ID endpoints"""
    print("\n🔄 Testing Alternative Endpoints")
    print("=" * 50)
    
    did_key = os.getenv("DID_API_KEY")
    if not did_key:
        print("❌ No API key available for testing")
        return False
    
    headers = {
        "Authorization": f"Basic {did_key}",
        "Content-Type": "application/json"
    }
    
    # Test different endpoints that might work
    test_endpoints = [
        ("Voices", "GET", "https://api.d-id.com/voices"),
        ("Actors", "GET", "https://api.d-id.com/actors"),
        ("Images", "GET", "https://api.d-id.com/images"),
    ]
    
    for name, method, url in test_endpoints:
        try:
            print(f"📡 Testing {name} endpoint...")
            
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            else:
                response = requests.post(url, headers=headers, timeout=10)
            
            print(f"  📊 {name} Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ {name} accessible")
            elif response.status_code == 401:
                print(f"  ❌ {name} - Authentication failed")
            elif response.status_code == 403:
                print(f"  ❌ {name} - Forbidden (check credits/permissions)")
            else:
                print(f"  ⚠️ {name} - Status {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {name} error: {e}")

def provide_solutions():
    """Provide solutions based on test results"""
    print("\n💡 Troubleshooting Solutions")
    print("=" * 50)
    
    solutions = [
        "1. 🔑 API Key Issues:",
        "   - Verify key is copied correctly from D-ID dashboard",
        "   - Check for extra spaces or quotes in .env file",
        "   - Ensure key starts with 'did_' or similar format",
        "",
        "2. 💰 Credits/Account Issues:",
        "   - Log into D-ID Studio to check remaining credits",
        "   - Verify trial account is still active",
        "   - Check if account needs verification",
        "",
        "3. 🌐 Network Issues:",
        "   - Try from different network/VPN",
        "   - Check firewall/antivirus blocking requests",
        "   - Verify internet connection stability",
        "",
        "4. 📋 Alternative Solutions:",
        "   - Try regenerating API key in D-ID dashboard",
        "   - Contact D-ID support if trial issues persist",
        "   - Consider testing with a different D-ID account",
        "",
        "5. 🔄 Workarounds:",
        "   - Use script+voice generation only (skip video)",
        "   - Test with different video generation services",
        "   - Focus on perfecting script/voice pipeline first"
    ]
    
    for solution in solutions:
        print(solution)

def main():
    """Run comprehensive D-ID API debugging"""
    print("🔍 D-ID API Comprehensive Debugging")
    print("=" * 60)
    print()
    
    # Run all tests
    tests = [
        test_environment_setup,
        test_basic_api_connection,
        test_account_info,
        test_file_upload,
        test_alternative_endpoints
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append(False)
        print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! D-ID API should work now.")
    elif passed > 0:
        print("⚠️ Some tests passed. Check specific failures above.")
    else:
        print("❌ All tests failed. See solutions below.")
    
    print()
    provide_solutions()

if __name__ == "__main__":
    main()
