import requests
import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_did_authentication():
    """Test D-ID authentication with current API key"""
    print("🔑 Testing D-ID Authentication")
    print("=" * 40)
    
    did_key = os.getenv("DID_API_KEY")
    print(f"API Key: {did_key[:20]}...")
    
    headers = {
        "Authorization": f"Basic {did_key}",
        "Content-Type": "application/json"
    }
    
    # Test basic endpoints
    endpoints = [
        ("Credits", "GET", "https://api.d-id.com/credits"),
        ("Talks List", "GET", "https://api.d-id.com/talks"),
        ("Account", "GET", "https://api.d-id.com/account")
    ]
    
    for name, method, url in endpoints:
        print(f"\n📡 Testing {name}...")
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            else:
                response = requests.post(url, headers=headers, timeout=10)
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ Success!")
                
                if name == "Credits":
                    remaining = data.get('remaining', 0)
                    print(f"  💰 Credits: {remaining}")
                elif name == "Talks List":
                    talks = data.get('talks', [])
                    print(f"  📊 Previous talks: {len(talks)}")
                else:
                    print(f"  📋 Data: {str(data)[:100]}...")
                    
            elif response.status_code == 403:
                print(f"  🚫 Forbidden - Check permissions")
            elif response.status_code == 401:
                print(f"  🔒 Unauthorized - Invalid API key")
            elif response.status_code >= 500:
                print(f"  🔥 Server Error - D-ID API issues")
            else:
                print(f"  ❓ Status {response.status_code}: {response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")

def test_simple_video_creation():
    """Test creating a simple video with text-to-speech"""
    print(f"\n🎬 Testing Simple Video Creation")
    print("=" * 40)
    
    did_key = os.getenv("DID_API_KEY")
    headers = {
        "Authorization": f"Basic {did_key}",
        "Content-Type": "application/json"
    }
    
    # Simple payload
    payload = {
        "script": {
            "type": "text",
            "input": "Hello! This is a test video. If you can see this, the D-ID API is working correctly.",
            "provider": {
                "type": "microsoft",
                "voice_id": "en-US-JennyNeural"
            }
        },
        "source_url": "https://create-images-results.d-id.com/DefaultFemale.png"
    }
    
    print("📤 Sending video creation request...")
    print(f"📝 Text: {payload['script']['input']}")
    
    try:
        response = requests.post(
            "https://api.d-id.com/talks",
            headers=headers,
            data=json.dumps(payload),
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            talk_id = data.get('id')
            print(f"✅ Video creation started!")
            print(f"🆔 Talk ID: {talk_id}")
            print(f"📊 Response: {data}")
            return talk_id
        elif response.status_code >= 500:
            print(f"🔥 Server Error: D-ID API is having issues")
            print(f"💡 This is temporary - try again in a few minutes")
        elif response.status_code == 403:
            print(f"🚫 Forbidden: Check account permissions/credits")
        elif response.status_code == 401:
            print(f"🔒 Unauthorized: Invalid API key")
        else:
            print(f"❌ Failed: {response.text}")
            
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    print("🚀 D-ID Simple API Test")
    print("=" * 30)
    
    # Test authentication first
    test_did_authentication()
    
    # Test video creation
    talk_id = test_simple_video_creation()
    
    if talk_id:
        print(f"\n🎉 SUCCESS! D-ID API is working!")
        print(f"💡 Your authentication is correct")
        print(f"📝 Talk ID: {talk_id}")
    else:
        print(f"\n⚠️ Video creation failed")
        print(f"💡 This might be due to:")
        print(f"   - D-ID server issues (500 errors)")
        print(f"   - Account limitations")
        print(f"   - Temporary API problems")
        print(f"   - Try again in a few minutes")

if __name__ == "__main__":
    main()
