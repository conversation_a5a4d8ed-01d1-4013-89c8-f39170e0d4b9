import requests
import base64
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def decode_basic_auth(encoded_key):
    """Decode base64 encoded email:password combination"""
    try:
        decoded = base64.b64decode(encoded_key).decode('utf-8')
        if ':' in decoded:
            email, password = decoded.split(':', 1)
            return email, password
        return None, None
    except Exception as e:
        print(f"❌ Error decoding key: {e}")
        return None, None

def test_basic_auth_login(email, password):
    """Try to login with email/password to get proper API key"""
    print(f"\n🔐 Attempting login with email: {email}")

    login_url = "https://api.d-id.com/auth/login"
    login_data = {
        "email": email,
        "password": password
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(login_url, json=login_data, headers=headers)
        print(f"Login response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")

            # Look for API key or token in response
            if 'token' in data:
                print(f"🔑 Found token: {data['token'][:20]}...")
                return data['token']
            elif 'api_key' in data:
                print(f"🔑 Found API key: {data['api_key'][:20]}...")
                return data['api_key']
            else:
                print("📋 Full response:")
                print(json.dumps(data, indent=2))
                return None
        else:
            print(f"❌ Login failed: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_api_key_formats(potential_key, email=None, password=None):
    """Test different API key formats and authentication methods"""
    print("\n🧪 Testing Different Authentication Methods")
    print("=" * 60)

    test_cases = [
        {
            "name": "Bearer Token (Standard)",
            "headers": {"Authorization": f"Bearer {potential_key}"},
            "url": "https://api.d-id.com/talks"
        },
        {
            "name": "Basic Auth (Original Key)",
            "headers": {"Authorization": f"Basic {potential_key}"},
            "url": "https://api.d-id.com/talks"
        },
        {
            "name": "API Key Header",
            "headers": {"X-API-Key": potential_key},
            "url": "https://api.d-id.com/talks"
        },
        {
            "name": "Query Parameter",
            "headers": {},
            "url": f"https://api.d-id.com/talks?key={potential_key}"
        }
    ]

    # If we have email/password, add basic auth test
    if email and password:
        auth_string = base64.b64encode(f"{email}:{password}".encode()).decode()
        test_cases.append({
            "name": "Basic Auth (Email:Password)",
            "headers": {"Authorization": f"Basic {auth_string}"},
            "url": "https://api.d-id.com/talks"
        })

    for test_case in test_cases:
        print(f"\n📡 Testing: {test_case['name']}")
        try:
            response = requests.get(
                test_case['url'],
                headers=test_case['headers'],
                timeout=10
            )

            print(f"  Status: {response.status_code}")

            if response.status_code == 200:
                print(f"  ✅ SUCCESS! This method works!")
                data = response.json()
                print(f"  📊 Response: {json.dumps(data, indent=2)[:200]}...")
                return test_case
            elif response.status_code == 401:
                print(f"  🔒 Unauthorized - Invalid credentials")
            elif response.status_code == 403:
                print(f"  🚫 Forbidden - Check account permissions/credits")
            else:
                print(f"  ❌ Failed: {response.text[:100]}...")

        except Exception as e:
            print(f"  ❌ Error: {e}")

    return None

def get_account_info(auth_method):
    """Get account information using successful auth method"""
    print(f"\n📊 Getting Account Information")
    print("=" * 40)

    endpoints = [
        ("Account", "https://api.d-id.com/account"),
        ("Credits", "https://api.d-id.com/credits"),
        ("Usage", "https://api.d-id.com/usage")
    ]

    for name, url in endpoints:
        try:
            response = requests.get(url, headers=auth_method['headers'], timeout=10)
            print(f"\n{name} ({response.status_code}):")

            if response.status_code == 200:
                data = response.json()
                print(json.dumps(data, indent=2))
            else:
                print(f"❌ {response.text}")

        except Exception as e:
            print(f"❌ Error getting {name}: {e}")

def main():
    print("🚀 D-ID API Key Generator & Tester")
    print("=" * 50)

    # Get the current key from environment
    current_key = os.getenv("DID_API_KEY", "******************************:IhYICzxX6ZzArCuEBvO_l")
    print(f"📋 Current key: {current_key[:20]}...{current_key[-10:] if len(current_key) > 30 else current_key}")

    # Try to decode if it looks like base64
    email, password = decode_basic_auth(current_key)

    if email and password:
        print(f"🔍 Decoded credentials - Email: {email}")

        # Try to login and get proper API key
        proper_key = test_basic_auth_login(email, password)

        if proper_key:
            print(f"\n🎉 Got proper API key: {proper_key[:20]}...")
            current_key = proper_key

    # Test different authentication methods
    working_method = test_api_key_formats(current_key, email, password)

    if working_method:
        print(f"\n🎯 Working authentication method: {working_method['name']}")

        # Get account info
        get_account_info(working_method)

        # Update .env file if we found a better key
        if working_method['name'] != "Bearer Token (Standard)" and 'Authorization' in working_method['headers']:
            auth_header = working_method['headers']['Authorization']
            if auth_header.startswith('Bearer '):
                new_key = auth_header.replace('Bearer ', '')
                print(f"\n💾 Recommended .env update:")
                print(f"DID_API_KEY={new_key}")
    else:
        print("\n❌ No working authentication method found!")
        print("\n💡 Suggestions:")
        print("1. Check your D-ID account dashboard for the correct API key")
        print("2. Ensure your account has credits/is active")
        print("3. Try generating a new API key from D-ID dashboard")
        print("4. Contact D-ID support if issues persist")

if __name__ == "__main__":
    main()
