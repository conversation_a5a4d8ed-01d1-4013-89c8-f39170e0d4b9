import requests

# 🔑 Paste your D-ID API key here:
DID_API_KEY = "your-api-key-here"  # <-- Replace this!

headers = {
    "Authorization": f"Bearer {DID_API_KEY}",
    "Content-Type": "application/json"
}

# Test by requesting the list of supported avatars
url = "https://api.d-id.com/avatars"

try:
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        print("✅ D-ID API key is valid. Avatars available:")
        avatars = response.json().get("avatars", [])
        for avatar in avatars[:5]:  # Show first 5 for brevity
            print(f"- {avatar['name']} ({avatar['avatar_id']})")
    else:
        print(f"❌ API call failed. Status Code: {response.status_code}")
        print("Response:", response.text)
except Exception as e:
    print("❌ Error:", e)
