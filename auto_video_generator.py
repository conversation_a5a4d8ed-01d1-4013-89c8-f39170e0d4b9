import os
import time
import json
import logging
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any
from contextlib import contextmanager
import tempfile
import shutil

import requests
from dotenv import load_dotenv
import google.generativeai as genai
from elevenlabs import ElevenLabs, VoiceSettings
from moviepy.editor import VideoFileClip
from instagrapi import Client
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('video_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class Config:
    """Configuration management class"""
    def __init__(self):
        self.gemini_api_key = self._get_required_env("GEMINI_API_KEY")
        self.elevenlabs_api_key = self._get_required_env("ELEVENLABS_API_KEY")
        self.did_api_key = self._get_required_env("DID_API_KEY")
        self.ig_username = self._get_required_env("IG_USERNAME")
        self.ig_password = self._get_required_env("IG_PASSWORD")

        # Optional configurations with defaults
        self.voice_model = os.getenv("VOICE_MODEL", "eleven_monolingual_v1")
        # Use a common ElevenLabs voice ID - this is Rachel's actual ID
        self.voice_name = os.getenv("VOICE_NAME", "21m00Tcm4TlvDq8ikWAM")
        self.video_fps = int(os.getenv("VIDEO_FPS", "24"))
        self.max_retries = int(os.getenv("MAX_RETRIES", "3"))
        self.retry_delay = int(os.getenv("RETRY_DELAY", "2"))
        self.request_timeout = int(os.getenv("REQUEST_TIMEOUT", "30"))

        # Voice generation settings
        self.voice_stability = float(os.getenv("VOICE_STABILITY", "0.71"))
        self.voice_similarity_boost = float(os.getenv("VOICE_SIMILARITY_BOOST", "0.5"))
        self.voice_style = float(os.getenv("VOICE_STYLE", "0.0"))
        self.voice_use_speaker_boost = os.getenv("VOICE_USE_SPEAKER_BOOST", "true").lower() == "true"

    def _get_required_env(self, key: str) -> str:
        """Get required environment variable or raise error"""
        value = os.getenv(key)
        if not value:
            raise ValueError(f"Required environment variable {key} is not set")
        return value

config = Config()

class HTTPClientManager:
    """Manages HTTP sessions with retry logic and proper error handling"""

    def __init__(self, max_retries: int = 3, timeout: int = 30):
        self.session = requests.Session()
        self.timeout = timeout

        # Configure retry strategy
        retry_strategy = Retry(
            total=max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"],
            backoff_factor=1
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    def post(self, url: str, **kwargs) -> requests.Response:
        """Make POST request with error handling"""
        try:
            kwargs.setdefault('timeout', self.timeout)
            response = self.session.post(url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP POST request failed: {e}")
            raise

    def get(self, url: str, **kwargs) -> requests.Response:
        """Make GET request with error handling"""
        try:
            kwargs.setdefault('timeout', self.timeout)
            response = self.session.get(url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP GET request failed: {e}")
            raise

    def close(self):
        """Close the session"""
        self.session.close()

@contextmanager
def temporary_directory():
    """Context manager for temporary directory that cleans up automatically"""
    temp_dir = tempfile.mkdtemp()
    try:
        yield Path(temp_dir)
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

class VideoGenerator:
    """Main video generation class with improved error handling and resource management"""

    def __init__(self, config: Config):
        self.config = config
        self.http_client = HTTPClientManager(
            max_retries=config.max_retries,
            timeout=config.request_timeout
        )
        self._setup_apis()

    def _setup_apis(self):
        """Initialize API configurations"""
        try:
            genai.configure(api_key=self.config.gemini_api_key)
            self.elevenlabs_client = ElevenLabs(api_key=self.config.elevenlabs_api_key)
            logger.info("APIs configured successfully")
        except Exception as e:
            logger.error(f"Failed to configure APIs: {e}")
            raise

    def generate_script(self, custom_prompt: Optional[str] = None, topic: Optional[str] = None,
                       tone: str = "motivational", duration: int = 30) -> str:
        """Generate script using Gemini with improved error handling and customization"""
        try:
            model = genai.GenerativeModel("models/gemini-1.5-flash-latest")

            if custom_prompt:
                prompt = custom_prompt
            else:
                # Build dynamic prompt based on parameters
                topic_text = f" about {topic}" if topic else ""
                prompt = (
                    f"Write a {duration}-second {tone} Instagram Reel script{topic_text}. "
                    f"CRITICAL: Keep it EXACTLY {duration} seconds when spoken (approximately {duration * 2.5:.0f} words maximum). "
                    f"Requirements:\n"
                    f"- Engaging hook in first 3 seconds\n"
                    f"- Clear, conversational tone\n"
                    f"- Strong call-to-action at the end\n"
                    f"- MUST be speakable in {duration} seconds\n"
                    f"- Concise and impactful\n"
                    f"- No filler words or unnecessary content\n"
                    f"- Focus on one key message only"
                )

            logger.info(f"Generating {tone} script with Gemini...")

            # Configure generation parameters for better quality
            generation_config = genai.types.GenerationConfig(
                temperature=0.8,  # Creative but not too random
                top_p=0.9,
                top_k=40,
                max_output_tokens=1000,
            )

            response = model.generate_content(prompt, generation_config=generation_config)

            if not response.text:
                raise ValueError("Empty response from Gemini API")

            script = response.text.strip()

            # Basic validation of script length (rough estimate: 150-200 words per minute)
            word_count = len(script.split())
            estimated_duration = (word_count / 150) * 60  # seconds

            if estimated_duration > duration * 1.5:
                logger.warning(f"Generated script might be too long: {estimated_duration:.1f}s estimated vs {duration}s target")
            elif estimated_duration < duration * 0.5:
                logger.warning(f"Generated script might be too short: {estimated_duration:.1f}s estimated vs {duration}s target")

            logger.info(f"Script generated successfully ({len(script)} characters, ~{word_count} words, ~{estimated_duration:.1f}s estimated)")
            return script

        except Exception as e:
            logger.error(f"Script generation failed: {e}")
            raise RuntimeError(f"Failed to generate script: {e}") from e

    def generate_voice(self, script_text: str, output_path: Optional[Path] = None) -> Path:
        """Generate voice using ElevenLabs with improved error handling"""
        try:
            if not script_text.strip():
                raise ValueError("Script text cannot be empty")

            if output_path is None:
                output_path = Path("voice.mp3")

            logger.info(f"Generating voice for {len(script_text)} characters of text...")

            # Generate audio using the ElevenLabs API with configurable settings
            response = self.elevenlabs_client.text_to_speech.convert(
                text=script_text,
                voice_id=self.config.voice_name,
                model_id=self.config.voice_model,
                voice_settings=VoiceSettings(
                    stability=self.config.voice_stability,
                    similarity_boost=self.config.voice_similarity_boost,
                    style=self.config.voice_style,
                    use_speaker_boost=self.config.voice_use_speaker_boost
                )
            )

            # Save the audio data
            with open(output_path, "wb") as f:
                for chunk in response:
                    f.write(chunk)

            # Verify file was created and has content
            if not output_path.exists() or output_path.stat().st_size == 0:
                raise RuntimeError("Audio file was not created or is empty")

            logger.info(f"Voice generated successfully: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Voice generation failed: {e}")
            raise RuntimeError(f"Failed to generate voice: {e}") from e

    def generate_video(self, script_text: str, output_path: Optional[Path] = None) -> Path:
        """Generate talking head video using D-ID with text-to-speech (bypasses upload issues)"""
        try:
            if output_path is None:
                output_path = Path("output.mp4")

            headers = {
                "Authorization": f"Basic {self.config.did_api_key}",
                "Content-Type": "application/json"
            }

            logger.info("Creating video with D-ID text-to-speech...")

            # Use text-to-speech instead of uploading audio file
            # This bypasses the upload endpoint that's causing issues

            # Create video with text-to-speech payload
            payload = {
                "script": {
                    "type": "text",
                    "input": script_text,
                    "provider": {
                        "type": "microsoft",
                        "voice_id": "en-US-JennyNeural"
                    }
                },
                "source_url": "https://create-images-results.d-id.com/DefaultFemale.png",
                "config": {
                    "fluent": True,
                    "pad_audio": 0.5,
                    "stitch": True,
                    "result_format": "mp4"
                }
            }

            logger.info("Creating video...")
            create_resp = self.http_client.post(
                "https://api.d-id.com/talks",
                headers=headers,
                data=json.dumps(payload)
            )

            create_data = create_resp.json()
            if "id" not in create_data:
                raise ValueError(f"Video creation failed: {create_data}")

            talk_id = create_data["id"]
            logger.info(f"Video creation started with ID: {talk_id}")

            # Poll for completion with timeout and progress tracking
            max_wait_time = 300  # 5 minutes
            start_time = time.time()
            last_status = None

            while time.time() - start_time < max_wait_time:
                try:
                    status_resp = self.http_client.get(
                        f"https://api.d-id.com/talks/{talk_id}",
                        headers={k: v for k, v in headers.items() if k != "Content-Type"}
                    )
                    status_data = status_resp.json()

                    status = status_data.get("status")

                    # Only log status changes to reduce noise
                    if status != last_status:
                        logger.info(f"Video status: {status}")
                        last_status = status

                    if status == "done":
                        if "result_url" not in status_data:
                            raise ValueError("Video completed but no result URL provided")

                        video_url = status_data["result_url"]
                        logger.info("Downloading generated video...")

                        # Download with progress tracking for large files
                        video_resp = self.http_client.get(video_url, stream=True)
                        total_size = int(video_resp.headers.get('content-length', 0))

                        with open(output_path, "wb") as f:
                            downloaded = 0
                            for chunk in video_resp.iter_content(chunk_size=8192):
                                if chunk:
                                    f.write(chunk)
                                    downloaded += len(chunk)
                                    if total_size > 0:
                                        progress = (downloaded / total_size) * 100
                                        if downloaded % (1024 * 1024) == 0:  # Log every MB
                                            logger.info(f"Download progress: {progress:.1f}%")

                        # Verify file was created and has content
                        if not output_path.exists() or output_path.stat().st_size == 0:
                            raise RuntimeError("Video file was not created or is empty")

                        logger.info(f"Video saved successfully: {output_path} ({output_path.stat().st_size} bytes)")
                        return output_path

                    elif status == "error":
                        error_msg = status_data.get("error", "Unknown error")
                        raise RuntimeError(f"Video generation failed: {error_msg}")

                    elif status in ["started", "processing"]:
                        # Show elapsed time for long operations
                        elapsed = time.time() - start_time
                        if elapsed > 30 and elapsed % 30 < self.config.retry_delay:  # Every 30 seconds
                            logger.info(f"Still processing... ({elapsed:.0f}s elapsed)")

                    time.sleep(self.config.retry_delay)

                except requests.exceptions.RequestException as e:
                    logger.warning(f"Status check failed, retrying: {e}")
                    time.sleep(self.config.retry_delay)

            raise TimeoutError(f"Video generation timed out after {max_wait_time} seconds")

        except Exception as e:
            logger.error(f"Video generation failed: {e}")
            raise RuntimeError(f"Failed to generate video: {e}") from e

    def reformat_for_instagram(self, video_path: Path, output_path: Optional[Path] = None) -> Path:
        """Format video for Instagram with improved error handling and resource management"""
        clip = None
        try:
            if not video_path.exists():
                raise FileNotFoundError(f"Video file not found: {video_path}")

            if output_path is None:
                output_path = Path("final_instagram.mp4")

            logger.info("Reformatting video for Instagram...")

            clip = VideoFileClip(str(video_path))

            # Validate video properties
            if clip.duration <= 0:
                raise ValueError("Video has invalid duration")

            # Create vertical format (9:16 aspect ratio for Instagram Reels)
            target_height = 1920
            target_width = 1080

            # Calculate scaling to maintain aspect ratio
            scale_factor = target_height / clip.h
            new_width = int(clip.w * scale_factor)

            if new_width >= target_width:
                # Scale and crop horizontally
                vertical_clip = clip.resize(height=target_height)
                vertical_clip = vertical_clip.crop(x_center=vertical_clip.w / 2, width=target_width)
            else:
                # Scale and add padding if needed
                vertical_clip = clip.resize(width=target_width)
                if vertical_clip.h < target_height:
                    # Add black bars if video is too short
                    from moviepy.editor import ColorClip, CompositeVideoClip
                    background = ColorClip(size=(target_width, target_height), color=(0, 0, 0), duration=vertical_clip.duration)
                    vertical_clip = CompositeVideoClip([background, vertical_clip.set_position('center')])

            # Write video with optimized settings
            vertical_clip.write_videofile(
                str(output_path),
                fps=self.config.video_fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Verify output file
            if not output_path.exists() or output_path.stat().st_size == 0:
                raise RuntimeError("Instagram video was not created or is empty")

            logger.info(f"Instagram video created successfully: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Instagram formatting failed: {e}")
            raise RuntimeError(f"Failed to format video for Instagram: {e}") from e
        finally:
            # Clean up video clip to free memory
            if clip:
                clip.close()

    def post_to_instagram(self, video_path: Path, caption: str) -> bool:
        """Post video to Instagram with improved error handling"""
        try:
            if not video_path.exists():
                raise FileNotFoundError(f"Video file not found: {video_path}")

            if not caption.strip():
                logger.warning("Empty caption provided")
                caption = "🎬 New video created with AI automation!"

            logger.info("Logging into Instagram...")

            cl = Client()
            cl.login(self.config.ig_username, self.config.ig_password)

            logger.info("Uploading video to Instagram Reels...")

            # Upload with error handling
            result = cl.clip_upload(str(video_path), caption=caption)

            if result:
                logger.info("Successfully uploaded to Instagram Reels!")
                return True
            else:
                raise RuntimeError("Upload failed - no result returned")

        except Exception as e:
            logger.error(f"Instagram upload failed: {e}")
            raise RuntimeError(f"Failed to upload to Instagram: {e}") from e

    def list_available_voices(self) -> list:
        """List available ElevenLabs voices"""
        try:
            voices = self.elevenlabs_client.voices.get_all()
            voice_list = []
            for voice in voices.voices:
                voice_list.append({
                    "id": voice.voice_id,
                    "name": voice.name,
                    "category": voice.category,
                    "description": getattr(voice, 'description', 'No description')
                })
            logger.info(f"Found {len(voice_list)} available voices")
            return voice_list
        except Exception as e:
            logger.error(f"Failed to list voices: {e}")
            return []

    def cleanup(self):
        """Clean up resources"""
        try:
            self.http_client.close()
            logger.info("Resources cleaned up successfully")
        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")

    def generate_complete_video(self, custom_prompt: Optional[str] = None,
                              topic: Optional[str] = None, tone: str = "motivational",
                              duration: int = 30, auto_post: bool = True,
                              skip_video: bool = False) -> Dict[str, Any]:
        """Complete video generation pipeline with comprehensive error handling"""
        results = {
            "success": False,
            "script": None,
            "voice_file": None,
            "raw_video": None,
            "instagram_video": None,
            "posted": False,
            "errors": []
        }

        try:
            with temporary_directory() as temp_dir:
                # Generate script with enhanced parameters
                script = self.generate_script(
                    custom_prompt=custom_prompt,
                    topic=topic,
                    tone=tone,
                    duration=duration
                )
                results["script"] = script

                # Generate voice
                voice_file = temp_dir / "voice.mp3"
                voice_file = self.generate_voice(script, voice_file)
                results["voice_file"] = str(voice_file)

                if not skip_video:
                    # Generate video using text-to-speech (bypasses upload issues)
                    raw_video = temp_dir / "raw_video.mp4"
                    raw_video = self.generate_video(script, raw_video)
                    results["raw_video"] = str(raw_video)

                    # Format for Instagram
                    instagram_video = Path("final_instagram.mp4")
                    instagram_video = self.reformat_for_instagram(raw_video, instagram_video)
                    results["instagram_video"] = str(instagram_video)
                else:
                    logger.info("Skipping video generation (audio-only mode)")
                    results["raw_video"] = "skipped"
                    results["instagram_video"] = str(voice_file)  # Use audio file as output

                # Post to Instagram if requested
                if auto_post:
                    posted = self.post_to_instagram(instagram_video, script)
                    results["posted"] = posted

                results["success"] = True
                logger.info("Video generation pipeline completed successfully!")

        except Exception as e:
            error_msg = f"Pipeline failed: {e}"
            logger.error(error_msg)
            results["errors"].append(error_msg)

        return results

def main():
    """Main function with comprehensive error handling and logging"""
    generator = None
    try:
        logger.info("Starting video generation automation...")

        # Initialize video generator
        generator = VideoGenerator(config)

        # Run complete pipeline
        results = generator.generate_complete_video(auto_post=True)

        if results["success"]:
            print("\n🎉 Video generation completed successfully!")
            print(f"📝 Script: {results['script'][:100]}...")
            print(f"🔊 Voice file: {results['voice_file']}")
            print(f"📽️ Raw video: {results['raw_video']}")
            print(f"📱 Instagram video: {results['instagram_video']}")
            print(f"� Posted to Instagram: {results['posted']}")
        else:
            print("\n❌ Video generation failed!")
            for error in results["errors"]:
                print(f"Error: {error}")
            return 1

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        print("\n⏹️ Process stopped by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
        print(f"\n💥 Unexpected error: {e}")
        return 1
    finally:
        if generator:
            generator.cleanup()

    return 0

# Alternative function for custom usage
def generate_video_custom(prompt: str = None, post_to_ig: bool = True) -> Dict[str, Any]:
    """
    Generate video with custom parameters

    Args:
        prompt: Custom script prompt (optional)
        post_to_ig: Whether to auto-post to Instagram

    Returns:
        Dictionary with generation results
    """
    generator = None
    try:
        generator = VideoGenerator(config)
        return generator.generate_complete_video(
            custom_prompt=prompt,
            auto_post=post_to_ig
        )
    finally:
        if generator:
            generator.cleanup()

# Credit-efficient testing function
def test_with_credits(topic: str = "productivity tips", duration: int = 20) -> Dict[str, Any]:
    """
    Generate a short video optimized for D-ID trial credits

    Args:
        topic: Video topic
        duration: Duration in seconds (recommended: 15-25 for trial)

    Returns:
        Dictionary with generation results
    """
    generator = None
    try:
        generator = VideoGenerator(config)

        logger.info(f"Credit-efficient test: {duration}s video about {topic}")
        logger.info(f"Estimated cost: ~{duration/30*4:.1f} credits")

        return generator.generate_complete_video(
            topic=topic,
            tone="energetic",
            duration=duration,
            auto_post=False  # Don't auto-post during testing
        )
    finally:
        if generator:
            generator.cleanup()

if __name__ == "__main__":
    import sys
    sys.exit(main())
