#!/usr/bin/env python3
"""
Open Source Video Generation Automation
Uses SadTalker instead of D-ID for completely free video generation
"""

import os
import sys
import json
import time
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
from contextlib import contextmanager

import requests
from dotenv import load_dotenv
import google.generativeai as genai
from elevenlabs import ElevenLabs, VoiceSettings
from moviepy.editor import VideoFileClip
from instagrapi import Client
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

# Import our SadTalker generator
from sadtalker_video_generator import SadTalkerVideoGenerator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('video_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class Config:
    """Configuration management class"""
    def __init__(self):
        self.gemini_api_key = self._get_required_env("GEMINI_API_KEY")
        self.elevenlabs_api_key = self._get_required_env("ELEVENLABS_API_KEY")
        self.ig_username = self._get_required_env("IG_USERNAME")
        self.ig_password = self._get_required_env("IG_PASSWORD")
        
        # Optional configuration with defaults
        self.voice_model = os.getenv("VOICE_MODEL", "eleven_monolingual_v1")
        self.voice_name = os.getenv("VOICE_NAME", "21m00Tcm4TlvDq8ikWAM")  # Rachel
        self.video_fps = int(os.getenv("VIDEO_FPS", "24"))
        self.max_retries = int(os.getenv("MAX_RETRIES", "3"))
        self.retry_delay = int(os.getenv("RETRY_DELAY", "2"))
        self.request_timeout = int(os.getenv("REQUEST_TIMEOUT", "30"))
        
        # Voice quality settings
        self.voice_stability = float(os.getenv("VOICE_STABILITY", "0.71"))
        self.voice_similarity_boost = float(os.getenv("VOICE_SIMILARITY_BOOST", "0.5"))
        self.voice_style = float(os.getenv("VOICE_STYLE", "0.0"))
        self.voice_use_speaker_boost = os.getenv("VOICE_USE_SPEAKER_BOOST", "true").lower() == "true"

    def _get_required_env(self, key: str) -> str:
        """Get required environment variable or raise error"""
        value = os.getenv(key)
        if not value:
            raise ValueError(f"Required environment variable {key} not found")
        return value

@contextmanager
def temporary_directory():
    """Context manager for temporary directory"""
    temp_dir = Path(tempfile.mkdtemp())
    try:
        yield temp_dir
    finally:
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            logger.warning(f"Failed to cleanup temp directory {temp_dir}: {e}")

class OpenSourceVideoGenerator:
    """Complete video generation pipeline using open-source tools"""
    
    def __init__(self, config: Config):
        self.config = config
        self.setup_apis()
        self.sadtalker = SadTalkerVideoGenerator()
        
    def setup_apis(self):
        """Initialize API clients with retry logic"""
        logger.info("Setting up APIs...")
        
        # Configure Gemini
        genai.configure(api_key=self.config.gemini_api_key)
        self.gemini_model = genai.GenerativeModel('gemini-pro')
        
        # Configure ElevenLabs
        self.elevenlabs_client = ElevenLabs(api_key=self.config.elevenlabs_api_key)
        
        # Setup HTTP session with retries
        self.http_client = requests.Session()
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.http_client.mount("http://", adapter)
        self.http_client.mount("https://", adapter)
        
        logger.info("APIs configured successfully")

    def generate_script(self, custom_prompt: Optional[str] = None, 
                       topic: Optional[str] = None, tone: str = "motivational", 
                       duration: int = 30) -> str:
        """Generate script using Gemini AI"""
        
        if custom_prompt:
            prompt = custom_prompt
        else:
            if not topic:
                topic = "productivity and success"
            
            prompt = f"""Create a {tone} {duration}-second video script about {topic}.
            
Requirements:
- Exactly {duration} seconds when spoken (approximately {duration * 2.5} words)
- {tone} and engaging tone
- Clear, conversational language
- Include a strong hook in the first 3 seconds
- End with a call to action
- No special formatting, just the spoken text
- Make it suitable for social media (Instagram/TikTok)

Topic: {topic}
Duration: {duration} seconds
Tone: {tone}

Script:"""

        logger.info(f"Generating {tone} script with Gemini...")
        
        try:
            response = self.gemini_model.generate_content(prompt)
            script = response.text.strip()
            
            # Estimate duration (rough calculation: ~2.5 words per second)
            word_count = len(script.split())
            estimated_duration = word_count / 2.5
            
            logger.info(f"Script generated successfully ({len(script)} characters, ~{word_count} words, ~{estimated_duration:.1f}s estimated)")
            return script
            
        except Exception as e:
            logger.error(f"Script generation failed: {e}")
            raise

    def generate_voice(self, text: str, output_path: Path) -> Path:
        """Generate voice using ElevenLabs"""
        logger.info(f"Generating voice for {len(text)} characters of text...")
        
        try:
            voice_settings = VoiceSettings(
                stability=self.config.voice_stability,
                similarity_boost=self.config.voice_similarity_boost,
                style=self.config.voice_style,
                use_speaker_boost=self.config.voice_use_speaker_boost
            )
            
            audio = self.elevenlabs_client.generate(
                text=text,
                voice=self.config.voice_name,
                model=self.config.voice_model,
                voice_settings=voice_settings
            )
            
            # Save audio to file
            with open(output_path, "wb") as f:
                for chunk in audio:
                    f.write(chunk)
            
            logger.info(f"Voice generated successfully: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Voice generation failed: {e}")
            raise

    def generate_video(self, audio_file: Path, output_path: Optional[Path] = None,
                      avatar_image: Optional[Path] = None) -> Path:
        """Generate talking head video using SadTalker (open-source)"""
        logger.info("🎬 Generating video with SadTalker (open-source)...")
        
        if output_path is None:
            output_path = Path("sadtalker_video.mp4")
        
        try:
            # Use SadTalker to generate the video
            video_path = self.sadtalker.generate_video(
                audio_file=audio_file,
                output_path=output_path,
                image_path=avatar_image
            )
            
            logger.info(f"✅ Video generated successfully: {video_path}")
            return video_path
            
        except Exception as e:
            logger.error(f"❌ Video generation failed: {e}")
            raise

    def reformat_for_instagram(self, input_video: Path, output_path: Path) -> Path:
        """Reformat video for Instagram (9:16 aspect ratio)"""
        logger.info("Reformatting video for Instagram...")
        
        try:
            clip = VideoFileClip(str(input_video))
            
            # Instagram Reels optimal dimensions
            target_width = 1080
            target_height = 1920
            
            # Calculate scaling to fit within target dimensions
            scale_factor = min(target_width / clip.w, target_height / clip.h)
            
            if scale_factor < 1:
                # Scale down if video is too large
                clip = clip.resize(scale_factor)
            
            # Create vertical format
            if clip.w / clip.h < target_width / target_height:
                # Video is too tall, scale by width
                vertical_clip = clip.resize(width=target_width)
                if vertical_clip.h < target_height:
                    # Add black bars if video is too short
                    from moviepy.editor import ColorClip, CompositeVideoClip
                    background = ColorClip(size=(target_width, target_height), color=(0, 0, 0), duration=vertical_clip.duration)
                    vertical_clip = CompositeVideoClip([background, vertical_clip.set_position('center')])
            else:
                # Video is too wide, scale by height and crop
                vertical_clip = clip.resize(height=target_height)
                if vertical_clip.w > target_width:
                    # Crop to center
                    vertical_clip = vertical_clip.crop(
                        x_center=vertical_clip.w/2, 
                        width=target_width
                    )
            
            # Ensure exact dimensions
            vertical_clip = vertical_clip.resize((target_width, target_height))
            
            # Write the final video
            vertical_clip.write_videofile(
                str(output_path),
                fps=self.config.video_fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Clean up
            clip.close()
            vertical_clip.close()
            
            logger.info(f"Video reformatted for Instagram: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Video reformatting failed: {e}")
            raise

    def post_to_instagram(self, video_path: Path, caption: str) -> bool:
        """Post video to Instagram"""
        logger.info("Posting to Instagram...")
        
        try:
            cl = Client()
            cl.login(self.config.ig_username, self.config.ig_password)
            
            # Upload video
            media = cl.clip_upload(str(video_path), caption)
            
            logger.info(f"Successfully posted to Instagram: {media.pk}")
            return True
            
        except Exception as e:
            logger.error(f"Instagram posting failed: {e}")
            return False

    def generate_complete_video(self, custom_prompt: Optional[str] = None,
                              topic: Optional[str] = None, tone: str = "motivational",
                              duration: int = 30, auto_post: bool = True,
                              avatar_image: Optional[Path] = None) -> Dict[str, Any]:
        """Complete video generation pipeline"""
        results = {
            "success": False,
            "script": None,
            "voice_file": None,
            "raw_video": None,
            "instagram_video": None,
            "posted": False,
            "errors": []
        }

        try:
            with temporary_directory() as temp_dir:
                # Generate script
                script = self.generate_script(
                    custom_prompt=custom_prompt,
                    topic=topic,
                    tone=tone,
                    duration=duration
                )
                results["script"] = script

                # Generate voice
                voice_file = temp_dir / "voice.mp3"
                voice_file = self.generate_voice(script, voice_file)
                results["voice_file"] = str(voice_file)

                # Generate video with SadTalker
                raw_video = temp_dir / "raw_video.mp4"
                raw_video = self.generate_video(voice_file, raw_video, avatar_image)
                results["raw_video"] = str(raw_video)

                # Format for Instagram
                instagram_video = Path("final_instagram_opensource.mp4")
                instagram_video = self.reformat_for_instagram(raw_video, instagram_video)
                results["instagram_video"] = str(instagram_video)

                # Post to Instagram if requested
                if auto_post:
                    posted = self.post_to_instagram(instagram_video, script)
                    results["posted"] = posted

                results["success"] = True
                logger.info("🎉 Open-source video generation pipeline completed successfully!")

        except Exception as e:
            error_msg = f"Pipeline failed: {e}"
            logger.error(error_msg)
            results["errors"].append(error_msg)

        return results

    def cleanup(self):
        """Clean up resources"""
        try:
            self.http_client.close()
            logger.info("Resources cleaned up successfully")
        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")

def main():
    """Main execution function"""
    logger.info("🚀 Starting open-source video generation automation...")
    
    try:
        config = Config()
        generator = OpenSourceVideoGenerator(config)
        
        # Generate video
        results = generator.generate_complete_video(
            topic="productivity tips",
            tone="energetic", 
            duration=30,
            auto_post=False  # Set to True to auto-post
        )
        
        if results["success"]:
            print(f"\n🎉 Video generation successful!")
            print(f"📹 Video: {results['instagram_video']}")
            print(f"📝 Script: {results['script'][:100]}...")
        else:
            print(f"\n❌ Video generation failed!")
            for error in results["errors"]:
                print(f"Error: {error}")
        
        return 0 if results["success"] else 1
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n❌ Video generation failed!")
        print(f"Error: {e}")
        return 1
    finally:
        try:
            generator.cleanup()
        except:
            pass

if __name__ == "__main__":
    sys.exit(main())
