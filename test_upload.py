import requests
import os
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

def create_test_audio():
    """Create a minimal test audio file"""
    test_file = Path("test_upload.mp3")
    
    # Create a minimal MP3 file for testing
    with open(test_file, "wb") as f:
        # Write minimal MP3 header and some data
        f.write(b'\xff\xfb\x90\x00')  # MP3 frame header
        f.write(b'\x00' * 1000)  # Some data to make it a reasonable size
    
    print(f"📄 Created test audio file: {test_file} ({test_file.stat().st_size} bytes)")
    return test_file

def test_upload_methods(test_file):
    """Test different upload authentication methods"""
    did_key = os.getenv("DID_API_KEY")
    
    if not did_key:
        print("❌ No DID_API_KEY found in environment")
        return
    
    print(f"🔑 Using API key: {did_key[:20]}...")
    
    # Test different authentication methods for upload
    test_methods = [
        {
            "name": "Basic Auth (No Content-Type)",
            "headers": {"Authorization": f"Basic {did_key}"}
        },
        {
            "name": "Basic Auth (With Content-Type)",
            "headers": {
                "Authorization": f"Basic {did_key}",
                "Content-Type": "application/json"
            }
        },
        {
            "name": "Bearer Token (No Content-Type)",
            "headers": {"Authorization": f"Bearer {did_key}"}
        },
        {
            "name": "API Key Header",
            "headers": {"X-API-Key": did_key}
        },
        {
            "name": "No Auth Headers",
            "headers": {}
        }
    ]
    
    for method in test_methods:
        print(f"\n📤 Testing upload with: {method['name']}")
        
        try:
            with open(test_file, "rb") as f:
                files = {"file": ("test.mp3", f, "audio/mpeg")}
                
                response = requests.post(
                    "https://api.d-id.com/uploads",
                    headers=method['headers'],
                    files=files,
                    timeout=30
                )
                
                print(f"  Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"  ✅ SUCCESS!")
                    data = response.json()
                    print(f"  📊 Response: {data}")
                    return method
                elif response.status_code == 401:
                    print(f"  🔒 Unauthorized")
                elif response.status_code == 403:
                    print(f"  🚫 Forbidden")
                    print(f"  📋 Response: {response.text}")
                else:
                    print(f"  ❌ Failed: {response.text}")
                    
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    return None

def test_alternative_upload_endpoints(test_file):
    """Test alternative upload endpoints or methods"""
    did_key = os.getenv("DID_API_KEY")
    
    print(f"\n🔄 Testing Alternative Upload Methods")
    print("=" * 50)
    
    # Try different endpoints or methods
    alternatives = [
        {
            "name": "Direct file upload with query param",
            "url": f"https://api.d-id.com/uploads?key={did_key}",
            "headers": {}
        },
        {
            "name": "Upload with Basic auth in URL",
            "url": "https://api.d-id.com/uploads",
            "headers": {"Authorization": f"Basic {did_key}"},
            "use_data": True  # Try sending as data instead of files
        }
    ]
    
    for alt in alternatives:
        print(f"\n📡 Testing: {alt['name']}")
        
        try:
            with open(test_file, "rb") as f:
                if alt.get('use_data'):
                    # Try sending as raw data
                    response = requests.post(
                        alt['url'],
                        headers=alt['headers'],
                        data=f.read(),
                        timeout=30
                    )
                else:
                    # Standard file upload
                    files = {"file": ("test.mp3", f, "audio/mpeg")}
                    response = requests.post(
                        alt['url'],
                        headers=alt['headers'],
                        files=files,
                        timeout=30
                    )
                
                print(f"  Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"  ✅ SUCCESS!")
                    data = response.json()
                    print(f"  📊 Response: {data}")
                    return alt
                else:
                    print(f"  ❌ Failed: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    return None

def main():
    print("🚀 D-ID Upload Endpoint Tester")
    print("=" * 40)
    
    # Create test file
    test_file = create_test_audio()
    
    try:
        # Test upload methods
        working_method = test_upload_methods(test_file)
        
        if not working_method:
            # Try alternative methods
            working_method = test_alternative_upload_endpoints(test_file)
        
        if working_method:
            print(f"\n🎉 Found working upload method: {working_method['name']}")
        else:
            print(f"\n❌ No working upload method found!")
            print(f"\n💡 Possible issues:")
            print(f"  - Account doesn't have upload permissions")
            print(f"  - Credits exhausted")
            print(f"  - API key format incorrect")
            print(f"  - Account needs verification")
            
    finally:
        # Clean up test file
        if test_file.exists():
            test_file.unlink()
            print(f"\n🧹 Cleaned up test file")

if __name__ == "__main__":
    main()
