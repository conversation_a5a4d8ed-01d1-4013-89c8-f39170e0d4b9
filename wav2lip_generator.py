import os
import sys
import subprocess
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Optional
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Wav2LipVideoGenerator:
    """Lightweight open-source video generator using Wav2Lip"""
    
    def __init__(self):
        self.wav2lip_path = Path("Wav2Lip")
        self.models_downloaded = False
        
    def setup_wav2lip(self):
        """Download and setup Wav2Lip if not already done"""
        logger.info("Setting up Wav2Lip...")
        
        if not self.wav2lip_path.exists():
            logger.info("Cloning Wav2Lip repository...")
            try:
                subprocess.run([
                    "git", "clone", 
                    "https://github.com/Rudrabha/Wav2Lip.git"
                ], check=True, cwd=".")
                logger.info("✅ Wav2Lip cloned successfully")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to clone Wav2Lip: {e}")
                return False
        
        # Install requirements
        requirements_file = self.wav2lip_path / "requirements.txt"
        if requirements_file.exists():
            logger.info("Installing Wav2Lip requirements...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
                ], check=True)
                logger.info("✅ Requirements installed")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to install requirements: {e}")
                return False
        
        return self.download_models()
    
    def download_models(self):
        """Download required models for Wav2Lip"""
        logger.info("Downloading Wav2Lip models...")
        
        models_dir = self.wav2lip_path / "checkpoints"
        models_dir.mkdir(exist_ok=True)
        
        # Required model files (smaller than SadTalker)
        models = {
            "wav2lip_gan.pth": "https://github.com/Rudrabha/Wav2Lip/releases/download/v1.0/wav2lip_gan.pth",
            "wav2lip.pth": "https://github.com/Rudrabha/Wav2Lip/releases/download/v1.0/wav2lip.pth"
        }
        
        for model_name, url in models.items():
            model_path = models_dir / model_name
            
            if model_path.exists():
                logger.info(f"✅ Model {model_name} already exists")
                continue
                
            logger.info(f"📥 Downloading {model_name}...")
            try:
                response = requests.get(url, stream=True)
                response.raise_for_status()
                
                with open(model_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.info(f"✅ Downloaded {model_name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to download {model_name}: {e}")
                return False
        
        self.models_downloaded = True
        logger.info("✅ All models downloaded successfully")
        return True
    
    def get_default_video(self):
        """Get or create a default talking head video"""
        default_video = Path("default_talking_head.mp4")
        
        if not default_video.exists():
            logger.info("📥 Creating default talking head video...")
            
            try:
                # Create a simple video with a static face
                self.create_simple_video(default_video)
                
            except Exception as e:
                logger.error(f"❌ Failed to create default video: {e}")
                # Download a sample video instead
                self.download_sample_video(default_video)
        
        return default_video
    
    def create_simple_video(self, output_path):
        """Create a simple talking head video using moviepy"""
        try:
            from moviepy.editor import ImageClip, ColorClip
            from PIL import Image, ImageDraw
            
            # Create a simple face image
            img_path = Path("temp_face.jpg")
            img = Image.new('RGB', (512, 512), color='lightblue')
            draw = ImageDraw.Draw(img)
            
            # Draw a simple face
            draw.ellipse([100, 100, 400, 400], fill='peachpuff', outline='black', width=3)
            draw.ellipse([150, 180, 200, 230], fill='black')  # Left eye
            draw.ellipse([300, 180, 350, 230], fill='black')  # Right eye
            draw.ellipse([225, 280, 275, 320], fill='pink')   # Nose
            draw.arc([180, 320, 320, 380], 0, 180, fill='black', width=5)  # Smile
            
            img.save(img_path)
            
            # Create a 5-second video from the image
            clip = ImageClip(str(img_path), duration=5)
            clip.write_videofile(str(output_path), fps=25, verbose=False, logger=None)
            
            # Clean up
            clip.close()
            img_path.unlink()
            
            logger.info("✅ Created simple talking head video")
            
        except ImportError:
            logger.error("❌ MoviePy not available, cannot create video")
            raise
    
    def download_sample_video(self, output_path):
        """Download a sample talking head video"""
        # Use a free sample video (you might want to replace this with your own)
        sample_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        
        try:
            response = requests.get(sample_url, stream=True)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info("✅ Downloaded sample video")
            
        except Exception as e:
            logger.error(f"❌ Failed to download sample video: {e}")
            raise
    
    def generate_video(self, audio_file: Path, output_path: Optional[Path] = None, 
                      video_path: Optional[Path] = None) -> Path:
        """Generate lip-synced video using Wav2Lip"""
        
        if not self.models_downloaded:
            if not self.setup_wav2lip():
                raise RuntimeError("Failed to setup Wav2Lip")
        
        if output_path is None:
            output_path = Path("wav2lip_output.mp4")
        
        if video_path is None:
            video_path = self.get_default_video()
        
        if not audio_file.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_file}")
        
        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        logger.info(f"🎬 Generating lip-synced video with Wav2Lip...")
        logger.info(f"📹 Input video: {video_path}")
        logger.info(f"🎵 Audio: {audio_file}")
        logger.info(f"📹 Output: {output_path}")
        
        # Prepare Wav2Lip command
        wav2lip_script = self.wav2lip_path / "inference.py"
        
        if not wav2lip_script.exists():
            raise FileNotFoundError(f"Wav2Lip inference script not found: {wav2lip_script}")
        
        # Create results directory
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        cmd = [
            sys.executable, str(wav2lip_script),
            "--checkpoint_path", str(self.wav2lip_path / "checkpoints" / "wav2lip_gan.pth"),
            "--face", str(video_path),
            "--audio", str(audio_file),
            "--outfile", str(output_path)
        ]
        
        try:
            logger.info("🚀 Running Wav2Lip...")
            result = subprocess.run(
                cmd, 
                cwd=str(self.wav2lip_path),
                capture_output=True, 
                text=True, 
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ Wav2Lip completed successfully")
                
                if output_path.exists():
                    logger.info(f"✅ Video saved as: {output_path}")
                    return output_path
                else:
                    raise RuntimeError("No video file generated by Wav2Lip")
            else:
                logger.error(f"❌ Wav2Lip failed:")
                logger.error(f"STDOUT: {result.stdout}")
                logger.error(f"STDERR: {result.stderr}")
                raise RuntimeError(f"Wav2Lip failed with return code {result.returncode}")
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Wav2Lip timed out")
            raise RuntimeError("Wav2Lip generation timed out")
        except Exception as e:
            logger.error(f"❌ Error running Wav2Lip: {e}")
            raise

def test_wav2lip():
    """Test Wav2Lip setup and generation"""
    logger.info("🧪 Testing Wav2Lip Video Generation")
    logger.info("=" * 50)
    
    generator = Wav2LipVideoGenerator()
    
    # Create a test audio file (you can replace this with your actual audio)
    test_audio = Path("test_audio.wav")
    
    if not test_audio.exists():
        logger.info("📝 Creating test audio file...")
        # You would typically use your ElevenLabs generated audio here
        logger.warning("⚠️ No test audio file found. Please provide an audio file.")
        return False
    
    try:
        # Generate video
        output_video = generator.generate_video(test_audio)
        
        if output_video.exists():
            logger.info(f"🎉 SUCCESS! Video generated: {output_video}")
            logger.info(f"📊 File size: {output_video.stat().st_size} bytes")
            return True
        else:
            logger.error("❌ Video file not created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_wav2lip()
