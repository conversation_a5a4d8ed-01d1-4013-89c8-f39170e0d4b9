2025-07-31 16:00:31,847 - WARNING - ElevenLabs not available: No module named 'elevenlabs.types'
2025-07-31 16:00:31,847 - WARNING - Voice generation will be disabled. Please install elevenlabs manually if needed.
2025-07-31 16:00:31,854 - INFO - APIs configured successfully (ElevenLabs not available)
2025-07-31 16:00:31,854 - INFO - Resources cleaned up successfully
2025-07-31 16:00:31,856 - INFO - APIs configured successfully (ElevenLabs not available)
2025-07-31 16:00:31,856 - INFO - Generating script with Gemini...
2025-07-31 16:00:31,856 - INFO - Script generated successfully (41 characters)
2025-07-31 16:00:31,856 - INFO - Resources cleaned up successfully
2025-07-31 16:01:15,939 - WARNING - ElevenLabs not available: No module named 'elevenlabs.types'
2025-07-31 16:01:15,939 - WARNING - Voice generation will be disabled. Please install elevenlabs manually if needed.
2025-07-31 16:01:15,941 - INFO - Starting video generation automation...
2025-07-31 16:01:15,941 - INFO - APIs configured successfully (ElevenLabs not available)
2025-07-31 16:01:15,951 - INFO - Generating script with Gemini...
2025-07-31 16:01:22,217 - INFO - Script generated successfully (2518 characters)
2025-07-31 16:01:22,218 - ERROR - Voice generation failed: ElevenLabs is not available. Please install it manually:
1. Enable Windows Long Path support
2. Run: pip install elevenlabs
3. Or use a different voice generation service
2025-07-31 16:01:22,218 - ERROR - Pipeline failed: Failed to generate voice: ElevenLabs is not available. Please install it manually:
1. Enable Windows Long Path support
2. Run: pip install elevenlabs
3. Or use a different voice generation service
2025-07-31 16:01:22,220 - INFO - Resources cleaned up successfully
2025-07-31 16:12:35,721 - INFO - APIs configured successfully
2025-07-31 16:12:35,723 - INFO - Resources cleaned up successfully
2025-07-31 16:12:35,725 - INFO - APIs configured successfully
2025-07-31 16:12:35,725 - INFO - Generating script with Gemini...
2025-07-31 16:12:35,726 - INFO - Script generated successfully (41 characters)
2025-07-31 16:12:35,726 - INFO - Resources cleaned up successfully
2025-07-31 16:15:21,191 - INFO - APIs configured successfully
2025-07-31 16:15:21,193 - INFO - Resources cleaned up successfully
2025-07-31 16:15:21,196 - INFO - APIs configured successfully
2025-07-31 16:15:21,196 - INFO - Generating motivational script with Gemini...
2025-07-31 16:15:21,197 - WARNING - Generated script might be too short: 3.2s estimated vs 30s target
2025-07-31 16:15:21,197 - INFO - Script generated successfully (41 characters, ~8 words, ~3.2s estimated)
2025-07-31 16:15:21,197 - INFO - Resources cleaned up successfully
2025-07-31 16:16:46,557 - INFO - Starting video generation automation...
2025-07-31 16:16:46,575 - INFO - APIs configured successfully
2025-07-31 16:16:46,578 - INFO - Generating motivational script with Gemini...
2025-07-31 16:16:51,563 - WARNING - Generated script might be too long: 86.8s estimated vs 30s target
2025-07-31 16:16:51,563 - INFO - Script generated successfully (1468 characters, ~217 words, ~86.8s estimated)
2025-07-31 16:16:51,564 - INFO - Generating voice for 1468 characters of text...
2025-07-31 16:16:51,564 - ERROR - Voice generation failed: 'ElevenLabs' object has no attribute 'generate'
2025-07-31 16:16:51,565 - ERROR - Pipeline failed: Failed to generate voice: 'ElevenLabs' object has no attribute 'generate'
2025-07-31 16:16:51,566 - INFO - Resources cleaned up successfully
2025-07-31 16:18:31,320 - INFO - Starting video generation automation...
2025-07-31 16:18:31,340 - INFO - APIs configured successfully
2025-07-31 16:18:31,345 - INFO - Generating motivational script with Gemini...
2025-07-31 16:18:36,214 - WARNING - Generated script might be too long: 78.8s estimated vs 30s target
2025-07-31 16:18:36,214 - INFO - Script generated successfully (1277 characters, ~197 words, ~78.8s estimated)
2025-07-31 16:18:36,216 - INFO - Generating voice for 1277 characters of text...
2025-07-31 16:18:36,729 - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/Rachel "HTTP/1.1 404 Not Found"
2025-07-31 16:18:36,731 - ERROR - Voice generation failed: headers: {'date': 'Thu, 31 Jul 2025 10:48:37 GMT', 'server': 'uvicorn', 'content-length': '99', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': 'c3253a87a889128ca91e322ab232901f', 'x-region': 'us-central1', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 404, body: {'detail': {'status': 'voice_not_found', 'message': 'A voice with the voice_id Rachel was not found.'}}
2025-07-31 16:18:36,734 - ERROR - Pipeline failed: Failed to generate voice: headers: {'date': 'Thu, 31 Jul 2025 10:48:37 GMT', 'server': 'uvicorn', 'content-length': '99', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': 'c3253a87a889128ca91e322ab232901f', 'x-region': 'us-central1', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 404, body: {'detail': {'status': 'voice_not_found', 'message': 'A voice with the voice_id Rachel was not found.'}}
2025-07-31 16:18:36,735 - INFO - Resources cleaned up successfully
2025-07-31 16:20:22,050 - INFO - Starting video generation automation...
2025-07-31 16:20:22,066 - INFO - APIs configured successfully
2025-07-31 16:20:22,069 - INFO - Generating motivational script with Gemini...
2025-07-31 16:20:27,384 - WARNING - Generated script might be too long: 109.6s estimated vs 30s target
2025-07-31 16:20:27,385 - INFO - Script generated successfully (1811 characters, ~274 words, ~109.6s estimated)
2025-07-31 16:20:27,385 - INFO - Generating voice for 1811 characters of text...
2025-07-31 16:20:50,838 - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-07-31 16:20:51,520 - INFO - Voice generated successfully: C:\Users\<USER>\AppData\Local\Temp\tmpynk2vtce\voice.mp3
2025-07-31 16:20:51,521 - INFO - Uploading audio to D-ID...
2025-07-31 16:21:02,767 - ERROR - HTTP POST request failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:21:02,768 - ERROR - Video generation failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:21:02,770 - ERROR - Pipeline failed: Failed to generate video: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:21:02,771 - INFO - Resources cleaned up successfully
2025-07-31 16:25:33,785 - INFO - APIs configured successfully
2025-07-31 16:25:33,793 - INFO - Generating energetic script with Gemini...
2025-07-31 16:25:36,269 - INFO - Script generated successfully (275 characters, ~38 words, ~15.2s estimated)
2025-07-31 16:25:36,269 - INFO - Generating voice for 275 characters of text...
2025-07-31 16:25:40,314 - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-07-31 16:25:40,403 - INFO - Voice generated successfully: C:\Users\<USER>\AppData\Local\Temp\tmpopwb6v6d\voice.mp3
2025-07-31 16:25:40,404 - INFO - Uploading audio to D-ID...
2025-07-31 16:25:47,984 - ERROR - HTTP POST request failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:25:47,984 - ERROR - Video generation failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:25:47,987 - ERROR - Pipeline failed: Failed to generate video: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:25:47,987 - INFO - Resources cleaned up successfully
2025-07-31 16:28:39,356 - INFO - APIs configured successfully
2025-07-31 16:28:39,356 - INFO - Credit-efficient test: 30s video about success mindset
2025-07-31 16:28:39,356 - INFO - Estimated cost: ~4.0 credits
2025-07-31 16:28:39,357 - INFO - Generating energetic script with Gemini...
2025-07-31 16:28:42,502 - INFO - Script generated successfully (590 characters, ~85 words, ~34.0s estimated)
2025-07-31 16:28:42,503 - INFO - Generating voice for 590 characters of text...
2025-07-31 16:28:50,235 - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-07-31 16:28:50,669 - INFO - Voice generated successfully: C:\Users\<USER>\AppData\Local\Temp\tmpnovhbjz5\voice.mp3
2025-07-31 16:28:50,669 - INFO - Uploading audio to D-ID...
2025-07-31 16:28:59,203 - ERROR - HTTP POST request failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:28:59,204 - ERROR - Video generation failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:28:59,205 - ERROR - Pipeline failed: Failed to generate video: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:28:59,205 - INFO - Resources cleaned up successfully
2025-07-31 16:42:38,020 - INFO - Starting video generation automation...
2025-07-31 16:42:38,029 - INFO - APIs configured successfully
2025-07-31 16:42:38,030 - INFO - Generating motivational script with Gemini...
2025-07-31 16:42:40,810 - INFO - Script generated successfully (478 characters, ~75 words, ~30.0s estimated)
2025-07-31 16:42:40,810 - INFO - Generating voice for 478 characters of text...
2025-07-31 16:42:46,627 - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-07-31 16:42:46,815 - INFO - Voice generated successfully: C:\Users\<USER>\AppData\Local\Temp\tmphwuyq3y5\voice.mp3
2025-07-31 16:42:46,816 - INFO - Uploading audio to D-ID...
2025-07-31 16:42:49,171 - ERROR - HTTP POST request failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:42:49,171 - ERROR - Video generation failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:42:49,172 - ERROR - Pipeline failed: Failed to generate video: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:42:49,173 - INFO - Resources cleaned up successfully
2025-07-31 16:52:24,076 - INFO - Starting video generation automation...
2025-07-31 16:52:24,083 - INFO - APIs configured successfully
2025-07-31 16:52:24,085 - INFO - Generating motivational script with Gemini...
2025-07-31 16:52:27,286 - INFO - Script generated successfully (732 characters, ~105 words, ~42.0s estimated)
2025-07-31 16:52:27,287 - INFO - Generating voice for 732 characters of text...
2025-07-31 16:52:36,197 - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-07-31 16:52:36,977 - INFO - Voice generated successfully: C:\Users\<USER>\AppData\Local\Temp\tmp8yqpyg8s\voice.mp3
2025-07-31 16:52:36,978 - INFO - Uploading audio to D-ID...
2025-07-31 16:52:45,767 - ERROR - HTTP POST request failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:52:45,767 - ERROR - Video generation failed: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:52:45,769 - ERROR - Pipeline failed: Failed to generate video: 403 Client Error: Forbidden for url: https://api.d-id.com/uploads
2025-07-31 16:52:45,769 - INFO - Resources cleaned up successfully
